import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Footer, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { SUPERVISOR_SUBTASK_LABELS, SupervisorSubTask } from '@/types/project';
import { useRef, useState, useEffect } from 'react';
import { sortPhasesByCanonicalOrder, splitIntoColumns } from '@/lib/supervisor-subtasks';

export type PhaseStatus = 'not_started' | 'booked' | 'pending' | 'in_progress' | 'complete' | 'defect';
export type PhaseStateRecord = Record<string, {
  status: PhaseStatus;
  contractor?: string;
  startDate?: string;
  endDate?: string;
}>; // key is SupervisorSubTask

const STATUS_LABEL: Record<PhaseStatus, string> = {
  not_started: 'Not Started',
  booked: 'Booked',
  pending: 'Pending',
  in_progress: 'In Progress',
  complete: 'Complete',
  defect: 'Defect',
};

const STATUS_TONE: Record<PhaseStatus, string> = {
  not_started: 'bg-muted text-muted-foreground',
  booked: 'bg-blue-100 text-blue-700 dark:bg-blue-950 dark:text-blue-300',
  pending: 'bg-amber-100 text-amber-700 dark:bg-amber-950 dark:text-amber-300',
  in_progress: 'bg-sky-100 text-sky-700 dark:bg-sky-950 dark:text-sky-300',
  complete: 'bg-emerald-100 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-300',
  defect: 'bg-rose-100 text-rose-700 dark:bg-rose-950 dark:text-rose-300',
};

export function getAllowedTransitions(current: PhaseStatus): PhaseStatus[] {
  // Allow users to select any status, no sequential restriction
  const allStatuses: PhaseStatus[] = ['not_started', 'booked', 'pending', 'in_progress', 'complete', 'defect'];
  // Return all statuses except the current one
  return allStatuses.filter(status => status !== current);
}

export function SupervisorPhaseDialog({
  open,
  onOpenChange,
  selectedPhases,
  phaseStates,
  onChange,
  onTransition,
  onUpdate,
}: {
  open: boolean;
  onOpenChange: (v: boolean) => void;
  selectedPhases: SupervisorSubTask[];
  phaseStates: PhaseStateRecord;
  onChange: (next: PhaseStateRecord) => void;
  onTransition?: (phaseKey: SupervisorSubTask, to: PhaseStatus, additionalData?: { contractor?: string; startDate?: string; endDate?: string }) => Promise<void>;
  onUpdate?: (phaseKey: SupervisorSubTask, data: { contractor?: string; startDate?: string; endDate?: string }) => Promise<void>;
}) {
  const updateTimeouts = useRef<Record<string, NodeJS.Timeout>>({});
  const [pendingChanges, setPendingChanges] = useState<Record<string, { status?: PhaseStatus; contractor?: string; startDate?: string; endDate?: string }>>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Reset pending changes when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setPendingChanges({});
      setHasUnsavedChanges(false);
      // Clear any pending timeouts
      Object.values(updateTimeouts.current).forEach(timeout => clearTimeout(timeout));
      updateTimeouts.current = {};
    }
  }, [open]);

  // Add keyboard shortcut for saving
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (open && event.ctrlKey && event.key === 's') {
        event.preventDefault();
        if (hasUnsavedChanges) {
          handleSaveChanges();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [open, hasUnsavedChanges]);

  const handleSaveChanges = async () => {
    try {
      // Save all pending changes
      for (const [phaseKey, changes] of Object.entries(pendingChanges)) {
        if (Object.keys(changes).length > 0) {
          const { status, ...dataChanges } = changes;

          if (status !== undefined) {
            // If status is changing, use the transition API which handles both status and data
            await onTransition?.(phaseKey as SupervisorSubTask, status, dataChanges);
          } else if (Object.keys(dataChanges).length > 0) {
            // If only data is changing (no status change), use the update API
            await onUpdate?.(phaseKey as SupervisorSubTask, dataChanges);
          }
        }
      }

      // Clear pending changes after successful save
      setPendingChanges({});
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Failed to save changes:', error);
      // Could add toast notification here for error handling
    }
  };

  const handleClose = () => {
    if (hasUnsavedChanges) {
      const confirmClose = window.confirm('You have unsaved changes. Are you sure you want to close without saving?');
      if (!confirmClose) {
        return;
      }
    }
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={(open) => open ? onOpenChange(true) : handleClose()}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Update Subtasks</DialogTitle>
        </DialogHeader>

        <div className="space-y-2 max-h-[60vh] overflow-auto pr-1">
          {selectedPhases.length === 0 && (
            <div className="text-sm text-muted-foreground">No phases selected.</div>
          )}
          {(() => {
            // Sort phases by canonical order and split into columns for column-major layout
            const sortedPhases = sortPhasesByCanonicalOrder(selectedPhases);
            const { left, right } = splitIntoColumns(sortedPhases);

            const renderPhaseColumn = (phases: string[]) => phases.map((phase) => {
            const current = (phaseStates[phase]?.status ?? 'not_started') as PhaseStatus;
            const allowed = getAllowedTransitions(current);
            const phaseData = phaseStates[phase] || { status: 'not_started' as PhaseStatus };

            const updatePhaseData = (updates: Partial<typeof phaseData>) => {
              const newPhaseStates = {
                ...phaseStates,
                [phase]: { ...phaseData, ...updates }
              };
              onChange(newPhaseStates);

              // Track pending changes for this phase
              const currentPending = pendingChanges[phase] || {};
              const newPending = { ...currentPending };

              // Only track changes if values have actually changed from original
              const originalPhaseData = phaseStates[phase] || { status: 'not_started' as PhaseStatus };
              let hasChanges = false;

              if (updates.status !== undefined && updates.status !== originalPhaseData.status) {
                newPending.status = updates.status;
                hasChanges = true;
              }
              if (updates.contractor !== undefined && updates.contractor !== originalPhaseData.contractor) {
                newPending.contractor = updates.contractor;
                hasChanges = true;
              }
              if (updates.startDate !== undefined && updates.startDate !== originalPhaseData.startDate) {
                newPending.startDate = updates.startDate;
                hasChanges = true;
              }
              if (updates.endDate !== undefined && updates.endDate !== originalPhaseData.endDate) {
                newPending.endDate = updates.endDate;
                hasChanges = true;
              }

              if (hasChanges) {
                setPendingChanges(prev => ({ ...prev, [phase]: newPending }));
                setHasUnsavedChanges(true);
              }
            };

            return (
              <div key={phase} className="p-4 rounded border space-y-3">
                <div className="flex items-center gap-3">
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate" title={(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}>
                      {(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}
                      {pendingChanges[phase]?.status !== undefined && (
                        <span className="text-orange-500 ml-1">*</span>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Current: {STATUS_LABEL[current]}
                      {pendingChanges[phase]?.status !== undefined && (
                        <span className="text-orange-600 ml-1">
                          → {STATUS_LABEL[pendingChanges[phase].status!]}
                        </span>
                      )}
                    </div>
                  </div>
                  <Badge className={`shrink-0 ${pendingChanges[phase]?.status !== undefined ? 'border-orange-300 bg-orange-50 text-orange-700' : STATUS_TONE[current]}`}>
                    {pendingChanges[phase]?.status !== undefined ? STATUS_LABEL[pendingChanges[phase].status!] : STATUS_LABEL[current]}
                  </Badge>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button size="sm" variant="outline">Change status</Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {allowed.map((to) => (
                        <DropdownMenuItem key={to} onClick={() => {
                          // Update local state and track as pending change
                          updatePhaseData({ status: to });
                        }}>
                          {STATUS_LABEL[to]}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* New input fields */}
                <div className="grid grid-cols-3 gap-3">
                  <div className="space-y-1">
                    <Label htmlFor={`contractor-${phase}`} className="text-xs">
                      Contractor
                      {pendingChanges[phase]?.contractor !== undefined && (
                        <span className="text-orange-500 ml-1">*</span>
                      )}
                    </Label>
                    <Input
                      id={`contractor-${phase}`}
                      placeholder="Enter contractor"
                      value={phaseData.contractor || ''}
                      onChange={(e) => updatePhaseData({ contractor: e.target.value })}
                      className={`h-8 text-xs ${pendingChanges[phase]?.contractor !== undefined ? 'border-orange-300 bg-orange-50' : ''}`}
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor={`start-date-${phase}`} className="text-xs">
                      Start Date
                      {pendingChanges[phase]?.startDate !== undefined && (
                        <span className="text-orange-500 ml-1">*</span>
                      )}
                    </Label>
                    <Input
                      id={`start-date-${phase}`}
                      type="date"
                      value={phaseData.startDate || ''}
                      onChange={(e) => updatePhaseData({ startDate: e.target.value })}
                      className={`h-8 text-xs ${pendingChanges[phase]?.startDate !== undefined ? 'border-orange-300 bg-orange-50' : ''}`}
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor={`end-date-${phase}`} className="text-xs">
                      End Date
                      {pendingChanges[phase]?.endDate !== undefined && (
                        <span className="text-orange-500 ml-1">*</span>
                      )}
                    </Label>
                    <Input
                      id={`end-date-${phase}`}
                      type="date"
                      value={phaseData.endDate || ''}
                      onChange={(e) => updatePhaseData({ endDate: e.target.value })}
                      className={`h-8 text-xs ${pendingChanges[phase]?.endDate !== undefined ? 'border-orange-300 bg-orange-50' : ''}`}
                    />
                  </div>
                </div>
              </div>
            );
            });

            return (
              <div className="flex gap-4">
                <div className="flex-1 space-y-2">
                  {renderPhaseColumn(left)}
                </div>
                <div className="flex-1 space-y-2">
                  {renderPhaseColumn(right)}
                </div>
              </div>
            );
          })()}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex items-center text-sm text-muted-foreground">
            {hasUnsavedChanges && (
              <span>You have unsaved changes</span>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleClose}>
              {hasUnsavedChanges ? 'Cancel' : 'Close'}
            </Button>
            <Button
              onClick={handleSaveChanges}
              disabled={!hasUnsavedChanges}
              title="Save changes (Ctrl+S)"
            >
              Save Changes {hasUnsavedChanges && `(${Object.keys(pendingChanges).length})`}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

