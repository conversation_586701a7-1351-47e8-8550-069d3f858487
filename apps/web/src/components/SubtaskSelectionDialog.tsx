import { useState } from 'react';
import { SUPERVISOR_SUBTASK_LABELS, SupervisorSubTask } from '@/types/project';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { SUPERVISOR_PHASES, splitIntoColumns } from '@/lib/supervisor-subtasks';
import { Api } from '@/lib/api';

export function SubtaskSelectionDialog({
  open,
  onOpenChange,
  onConfirm,
  projectId,
}: {
  open: boolean;
  onOpenChange: (v: boolean) => void;
  onConfirm: (selectedTasks: string[]) => void;
  projectId?: string; // Optional for backward compatibility
}) {
  const [selected, setSelected] = useState<string[]>([]);

  const toggle = (phase: string) => {
    setSelected((prev) => prev.includes(phase) ? prev.filter(p => p !== phase) : [...prev, phase]);
  };

  const handleConfirm = async () => {
    if (selected.length === 0) return;

    // Use new API if projectId is provided, otherwise fall back to legacy callback
    if (projectId) {
      try {
        await Api.setSupervisorSubtaskSelections(projectId, selected);
        onOpenChange(false);
        setSelected([]);
        // Call the callback to refresh the parent component
        onConfirm(selected);
      } catch (error) {
        console.error('Failed to save subtask selections:', error);
        // Fall back to legacy callback on error
        onConfirm(selected);
        onOpenChange(false);
        setSelected([]);
      }
    } else {
      // Legacy behavior
      onConfirm(selected);
      onOpenChange(false);
      setSelected([]);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setSelected([]);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Select Subtasks</DialogTitle>
        </DialogHeader>
        <div className="space-y-3">
          <div className="text-sm text-muted-foreground">
            Select the subtasks you want to manage for this project.
          </div>
          <div>
            <label className="text-sm font-medium">Available subtasks</label>
            <div className="flex gap-4 max-h-64 overflow-auto mt-2 border rounded-md p-3">
              {(() => {
                const { left, right } = splitIntoColumns(SUPERVISOR_PHASES);
                return (
                  <>
                    <div className="flex-1 space-y-2">
                      {left.map((phase) => (
                        <label key={phase} className="flex items-center gap-2 text-sm">
                          <Checkbox checked={selected.includes(phase)} onCheckedChange={() => toggle(phase)} />
                          <span>{(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}</span>
                        </label>
                      ))}
                    </div>
                    <div className="flex-1 space-y-2">
                      {right.map((phase) => (
                        <label key={phase} className="flex items-center gap-2 text-sm">
                          <Checkbox checked={selected.includes(phase)} onCheckedChange={() => toggle(phase)} />
                          <span>{(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}</span>
                        </label>
                      ))}
                    </div>
                  </>
                );
              })()}
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={handleCancel}>Cancel</Button>
            <Button disabled={selected.length === 0} onClick={handleConfirm}>
              Confirm Selection ({selected.length} selected)
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
