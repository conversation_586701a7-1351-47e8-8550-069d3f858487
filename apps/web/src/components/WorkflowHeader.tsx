import { User, UserRole, ROLE_LABELS, getBaseRole } from "@/types/project";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Building2, UserCircle, BarChart3, FolderKanban, Users } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import { CustomUserButton } from "./CustomUserButton";
import { useDisplayName } from "@/hooks/useDisplayName";

interface WorkflowHeaderProps {
  currentUser?: User; // Optional for now, will be replaced with Clerk user
  currentView: UserRole;
  onRoleChange: (role: UserRole) => void;
}

export const WorkflowHeader = ({ currentUser, currentView, onRoleChange }: WorkflowHeaderProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { getDisplayText } = useDisplayName();
  // Only show base roles in view switcher
  const viewRoles: UserRole[] = ['sales', 'designer', 'supervisor'];

  const getRoleBadgeVariant = (role: UserRole) => {
    switch (role) {
      case 'sales':
        return 'bg-role-sales text-white';
      case 'designer':
        return 'bg-role-designer text-white';
      case 'supervisor':
        return 'bg-role-supervisor text-white';
      case 'manager':
        return 'secondary';
      case 'owner':
        return 'bg-yellow-500 text-white';
      default: return 'secondary';
    }
  };

  return (
    <header className="border-b bg-card shadow-sm">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <Building2 className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold text-foreground">Workflow Manager</h1>
                <p className="text-sm text-muted-foreground">Project Management System</p>
              </div>
            </div>

            <nav className="flex items-center gap-2">
              <Button
                variant={location.pathname === '/' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => navigate('/')}
                className="flex items-center gap-2"
              >
                <FolderKanban className="h-4 w-4" />
                Projects
              </Button>
            </nav>

                <Button
                  variant={location.pathname === '/case-history' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => navigate('/case-history')}
                  className="flex items-center gap-2"
                >
                  <BarChart3 className="h-4 w-4" />
                  Case History
                </Button>

                {/* User Management - Only for owners */}
                {currentUser && currentUser.role === 'owner' && (
                  <Button
                    variant={location.pathname === '/users' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => navigate('/users')}
                    className="flex items-center gap-2"
                  >
                    <Users className="h-4 w-4" />
                    Users
                  </Button>
                )}

          </div>

          <div className="flex items-center gap-4">
            {/* User Profile and Sign Out */}
            <div className="flex items-center gap-4">
              {currentUser && (
                <div className="flex items-center gap-2">
                  <UserCircle className="h-5 w-5 text-muted-foreground" />
                  <span className="text-sm font-medium">{getDisplayText()}</span>
                  <Badge className={getRoleBadgeVariant(currentUser.role)}>
                    {ROLE_LABELS[currentUser.role]}
                  </Badge>
                </div>
              )}
              <CustomUserButton
                afterSignOutUrl="/sign-in"
                appearance={{
                  elements: {
                    avatarBox: "h-8 w-8"
                  }
                }}
              />
            </div>

            <div className="flex gap-1 bg-muted p-1 rounded-lg">
              <span className="text-xs text-muted-foreground px-2 py-1">View:</span>
              {viewRoles.map((role) => (
                <Button
                  key={role}
                  variant={currentView === role ? "default" : "ghost"}
                  size="sm"
                  onClick={() => onRoleChange(role)}
                  className="text-xs px-3"
                >
                  {ROLE_LABELS[role]}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};